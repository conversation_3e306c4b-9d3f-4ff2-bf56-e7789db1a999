<?php

namespace App\Http\Controllers;

use App\Enums\Role;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AuthController extends Controller
{
    public function login(): View
    {
        return view('auth.login');
    }

    public function authenticate(Request $request): RedirectResponse
    {
        if (Auth::attempt($request->only('login', 'password'))) {
            $user = Auth::user();

            // Redirect car-park users to vehicles page instead of dashboard
            if ($user->role === Role::CAR_PARK->value) {
                return to_route('vehicles.index')->with('success', 'Přihlášení prob<PERSON>hlo ú<PERSON>');
            }

            return to_route('dashboard')->with('success', 'P<PERSON>ih<PERSON><PERSON>šení proběhlo ú<PERSON>');
        } else {
            return back()->with('error', 'Nespr<PERSON>vn<PERSON> p<PERSON><PERSON>šovací údaje')->withInput();
        }
    }

    public function logout(Request $request): RedirectResponse
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('dashboard');
    }
}
