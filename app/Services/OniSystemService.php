<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use Illuminate\Support\Facades\Log;

class OniSystemService
{
    private Client $client;

    public function __construct()
    {
        $this->client = new Client([
            'verify' => env('ONI_SYSTEM_SSL_VERIFY', false), // Configurable SSL verification
            'timeout' => env('ONI_SYSTEM_TIMEOUT', 30),
            'connect_timeout' => env('ONI_SYSTEM_CONNECT_TIMEOUT', 10),
            'headers' => [
                'User-Agent' => 'Laravel Application',
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            ],
        ]);
    }

    public static function getBaseUri(): string
    {
        return 'https://www.onisystem.net/inetgweb/ws/driveexp.jsp';
    }

    public static function getFormParams(): array
    {
        return [
            'IDOWN' => env('ONI_SYSTEM_IDOWN'),
            'WORK' => env('ONI_SYSTEM_WORK'),
            'USER' => env('ONI_SYSTEM_USER'),
            'PASSW' => env('ONI_SYSTEM_PASSW'),
            'ACT' => 'listobj',
        ];
    }

    /**
     * Get list of vehicles from ONI system
     * @throws GuzzleException
     */
    public function getVehicleList(): string
    {
        try {
            Log::info('ONI System: Attempting to fetch vehicle list', [
                'url' => self::getBaseUri(),
                'params' => array_keys(self::getFormParams()) // Log param keys only for security
            ]);

            $response = $this->client->post(self::getBaseUri(), [
                RequestOptions::FORM_PARAMS => self::getFormParams(),
            ]);

            $data = $response->getBody()->getContents();

            Log::info('ONI System: Successfully fetched vehicle list', [
                'response_length' => strlen($data),
                'status_code' => $response->getStatusCode()
            ]);

            return $data;
        } catch (GuzzleException $e) {
            Log::error('ONI System: Failed to fetch vehicle list', [
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw $e;
        }
    }
}
