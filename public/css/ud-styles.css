/*===========================
  COMMON css
===========================*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@200;300;400;500;600;700;800;900&display=swap");
:root {
  --font: "Inter", sans-serif;
  --body-color: #637381;
  --heading-color: #212b36;
  --primary-color: #3056d3;
  --white: #ffffff;
}

.text-primary {
    color: #3056d3
}

body {
  font-family: var(--font);
  font-weight: normal;
  font-style: normal;
  color: var(--body-color);
  overflow-x: hidden;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

img {
  max-width: 100%;
}

a,
button,
input,
textarea {
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

a,
a:focus,
input:focus,
textarea:focus,
button:focus,
.navbar-toggler:focus {
  text-decoration: none;
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}

a:focus,
a:hover {
  text-decoration: none;
}

i,
span,
a {
  display: inline-block;
}

audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  color: var(--heading-color);
  margin: 0;
}

h1 {
  font-size: 48px;
}

h2 {
  font-size: 36px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 22px;
}

ul,
ol {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

p {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: var(--body-color);
  margin: 0;
}

/*===== All Button Style =====*/
.ud-main-btn {
  display: inline-block;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  font-weight: 500;
  font-size: 16px;
  border-radius: 5px;
  padding: 15px 25px;
  border: 1px solid transparent;
  color: var(--white);
  cursor: pointer;
  z-index: 5;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
  background: var(--primary-color);
}

.ud-main-btn:hover {
  color: var(--white);
  background: var(--heading-color);
}

@media (max-width: 767px) {
  .container {
    padding-left: 40px;
    padding-right: 40px;
  }
  .container-nav {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .container {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.back-to-top {
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: var(--primary-color);
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  text-align: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 5px;
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 99;
  color: var(--white);
}

.back-to-top:hover {
  color: var(--white);
  background: var(--heading-color);
}

/* ===== Header CSS ===== */
.ud-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

.navbar {
  padding: 0 0;
  border-radius: 5px;
  position: relative;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

.navbar-brand {
  padding: 0;
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .navbar-brand {
    padding: 5px 0;
  }
}

.navbar-brand img {
  max-width: 60px;
}

.navbar-toggler {
  padding: 0;
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .navbar-collapse {
    position: absolute;
    top: 100%;
    right: 0;
    max-width: 300px;
    width: 100%;
    background-color: var(--white);
    z-index: 9;
    -webkit-box-shadow: 0 15px 20px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 15px 20px 0 rgba(0, 0, 0, 0.1);
    padding: 0;
    display: none;
    border-radius: 5px;
  }
  .navbar-collapse.show {
    display: block;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .navbar-nav {
    padding: 20px 30px;
  }
}

.navbar-nav .nav-item {
  position: relative;
  padding: 0 20px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .navbar-nav .nav-item {
    padding: 0 12px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .navbar-nav .nav-item {
    padding: 0;
  }
}

.navbar-nav .nav-item > a {
  font-size: 16px;
  font-weight: 400;
  color: var(--white);
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  padding: 25px 8px;
  position: relative;
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .navbar-nav .nav-item > a {
    display: block;
    padding: 8px 0;
    color: var(--heading-color);
  }
}

.navbar-nav .nav-item > a:hover, .navbar-nav .nav-item > a.active {
  color: var(--white);
  opacity: 0.5;
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .navbar-nav .nav-item > a:hover, .navbar-nav .nav-item > a.active {
    color: var(--primary-color);
    opacity: 1;
  }
}

.navbar-nav .nav-item:hover > a {
  color: var(--white);
  opacity: 0.5;
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .navbar-nav .nav-item:hover > a {
    color: var(--primary-color);
    opacity: 1;
  }
}

.navbar-nav .nav-item.nav-item-has-children > a {
  position: relative;
}

.navbar-nav .nav-item.nav-item-has-children > a::after {
  content: "";
  box-sizing: border-box;
  position: absolute;
  width: 10px;
  height: 10px;
  border-bottom: 2px solid;
  border-right: 2px solid;
  transform: rotate(45deg) translateY(-50%);
  right: -15px;
  top: 50%;
  margin-top: -2px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .navbar-nav .nav-item.nav-item-has-children > a::after {
    right: 5px;
  }
}

.navbar-btn .ud-main-btn {
  padding: 8px 16px;
  background: transparent;
}

.ud-sticky {
    position: fixed;
    z-index: 99;
    background-color: rgba(255, 255, 255, 0.8);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    -webkit-box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, 0.1);
    box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, 0.1);
    -webkit-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}

.ud-sticky .navbar {
    padding: 0;
}

.ud-sticky .navbar-nav .nav-item:hover > a {
  color: var(--primary-color);
  opacity: 1;
}

.ud-sticky .navbar-nav .nav-item a {
  color: var(--heading-color);
}

.ud-sticky .navbar-nav .nav-item a.active, .ud-sticky .navbar-nav .nav-item a:hover {
  color: var(--primary-color);
  opacity: 1;
}

/* ===== Hero CSS ===== */
.ud-hero {
  background: var(--primary-color);
  padding-top: 180px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ud-hero {
    padding-top: 160px;
  }
}

@media (max-width: 767px) {
  .ud-hero {
    padding-top: 130px;
  }
}

.ud-hero-content {
  max-width: 780px;
  margin: auto auto 30px;
}

.ud-hero-content .ud-hero-title {
  color: var(--white);
  font-weight: 700;
  font-size: 55px;
  line-height: 65px;
  text-align: center;
  margin-bottom: 30px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ud-hero-content .ud-hero-title {
    font-size: 44px;
    line-height: 56px;
  }
}

@media (max-width: 767px) {
  .ud-hero-content .ud-hero-title {
    font-size: 32px;
    line-height: 44px;
  }
}

.ud-hero-content .ud-hero-desc {
  font-weight: normal;
  font-size: 20px;
  line-height: 35px;
  text-align: center;
  color: var(--white);
  opacity: 0.8;
  max-width: 600px;
  margin: auto auto 30px;
}

@media (max-width: 767px) {
  .ud-hero-content .ud-hero-desc {
    font-size: 16px;
    line-height: 30px;
  }
}

/* ===== Features CSS ===== */
.ud-features {
  padding-top: 120px;
  padding-bottom: 90px;
}

@media (max-width: 767px) {
  .ud-features {
    padding-top: 80px;
    padding-bottom: 40px;
  }
}

.ud-section-title {
  max-width: 620px;
  margin-bottom: 70px;
}

.ud-section-title span {
  font-weight: 600;
  font-size: 18px;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.ud-section-title h2 {
  font-weight: 700;
  font-size: 42px;
  line-height: 55px;
  color: var(--heading-color);
  margin-bottom: 20px;
}

.ud-section-title p {
  font-size: 20px;
  line-height: 30px;
}

@media (max-width: 767px) {
  .ud-section-title p {
    font-size: 16px;
    line-height: 26px;
  }
}

.ud-single-feature {
  margin-bottom: 40px;
}

.ud-single-feature:hover .ud-feature-icon::before {
  transform: rotate(45deg);
}

.ud-single-feature .ud-feature-icon {
  width: 70px;
  height: 70px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 14px;
  background: var(--primary-color);
  font-size: 32px;
  color: var(--white);
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 40px;
}

.ud-single-feature .ud-feature-icon::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 14px;
  background: var(--primary-color);
  opacity: 0.2;
  z-index: -1;
  transform: rotate(23deg);
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

.ud-single-feature .ud-feature-title {
  font-weight: 700;
  font-size: 20px;
  line-height: 28px;
  margin-bottom: 12px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .ud-single-feature .ud-feature-title {
    font-size: 18px;
    line-height: 26px;
  }
}

.ud-single-feature .ud-feature-desc {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 40px;
}

.ud-single-feature .ud-feature-link {
  font-weight: 500;
  color: var(--body-color);
}

.ud-single-feature .ud-feature-link:hover {
  color: var(--primary-color);
}

/* ===== About CSS ===== */
.ud-about {
  background: #f3f4fe;
  padding: 120px 0;
}

@media (max-width: 767px) {
  .ud-about {
    padding: 80px 0;
  }
}

.ud-about-wrapper {
  background: var(--white);
  border: 1px solid rgba(0, 0, 0, 0.08);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .ud-about-wrapper {
    display: block;
  }
}

.ud-about-content-wrapper {
  padding: 70px;
}

@media (max-width: 767px) {
  .ud-about-content-wrapper {
    padding: 50px 30px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .ud-about-content-wrapper {
    padding: 50px;
  }
}

.ud-about-content {
  max-width: 500px;
}

.ud-about-content .tag {
  font-weight: 500;
  font-size: 14px;
  color: var(--white);
  background: var(--primary-color);
  padding: 5px 20px;
  display: inline-block;
  margin-bottom: 20px;
}

.ud-about-content h2 {
  font-weight: bold;
  font-size: 36px;
  line-height: 45px;
  margin-bottom: 30px;
}

@media (max-width: 767px) {
  .ud-about-content h2 {
    font-size: 26px;
    line-height: 38px;
  }
}

.ud-about-content p {
  font-size: 16px;
  line-height: 28px;
  margin-bottom: 40px;
}

.ud-about-content .ud-main-btn:hover {
  background: #2748b4;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.ud-about-image-desktop {
  display: block;
  width: 30%;
  position: relative;
}

.ud-about-image-mobile {
  display: none;
}

@media only screen and (max-width: 991px) {
  .ud-about-image-mobile {
    display: block;
    text-align: center;
      width: 100%;
  }
    .ud-about-image-desktop {
        display: none;
    }
}

/* ===== Pricing CSS ===== */
.ud-pricing {
  padding-top: 120px;
  padding-bottom: 90px;
}

@media (max-width: 767px) {
  .ud-pricing {
    padding-top: 80px;
    padding-bottom: 40px;
  }
}

.ud-single-pricing {
  margin-bottom: 40px;
  background: var(--white);
  border: 2px solid #d4deff;
  border-radius: 8px;
  text-align: center;
  padding: 45px 30px;
  position: relative;
}

.ud-single-pricing.active {
  background: linear-gradient(180deg, #3056d3 0%, #179bee 100%);
  border: none;
  padding: 55px 30px;
}

.ud-single-pricing.active .ud-pricing-header h3,
.ud-single-pricing.active .ud-pricing-header h4 {
  color: var(--white);
}

.ud-single-pricing.active .ud-pricing-body li {
  color: var(--white);
}

.ud-single-pricing.first-item::after, .ud-single-pricing.last-item::after {
  content: "";
  position: absolute;
  width: 55px;
  height: 55px;
  z-index: 1;
}

.ud-single-pricing.first-item::after {
  background: var(--primary-color);
  left: -2px;
  bottom: -2px;
  border-radius: 0 55px 0 8px;
}

.ud-single-pricing.last-item::after {
  background: var(--primary-color);
  top: -2px;
  right: -2px;
  border-radius: 0 8px 0 55px;
}

.ud-single-pricing .ud-popular-tag {
  display: inline-block;
  padding: 10px 25px;
  background: var(--white);
  border-radius: 30px;
  color: var(--primary-color);
  margin-bottom: 20px;
  font-weight: 600;
}

.ud-single-pricing .ud-pricing-header h3 {
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 8px;
}

.ud-single-pricing .ud-pricing-header h4 {
  font-weight: 600;
  font-size: 26px;
  color: var(--primary-color);
  margin-bottom: 40px;
}

.ud-single-pricing .ud-pricing-body {
  margin-bottom: 40px;
}

.ud-single-pricing .ud-pricing-body li {
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 18px;
}

.ud-single-pricing .ud-main-btn {
  border-radius: 30px;
  padding: 15px 40px;
}

.ud-single-pricing .ud-border-btn {
  border: 1px solid #d4deff;
  color: var(--primary-color);
  background: var(--white);
}

.ud-single-pricing .ud-border-btn:hover {
  color: var(--white);
  border-color: var(--primary-color);
  background: var(--primary-color);
}

.ud-brands .ud-title {
  margin-bottom: 30px;
}

.ud-brands .ud-title h6 {
  font-weight: normal;
  font-size: 12px;
  color: var(--body-color);
  display: inline-block;
  position: relative;
}

.ud-brands .ud-title h6::after {
  content: "";
  position: absolute;
  width: 30px;
  height: 1px;
  background: #afb2b5;
  right: -40px;
  top: 50%;
}

.ud-brands .ud-brands-logo .ud-single-logo:hover img {
  -webkit-filter: none;
          filter: none;
}

.ud-brands .ud-brands-logo .ud-single-logo img {
  -webkit-filter: grayscale(1);
          filter: grayscale(1);
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

/* ===== Team CSS ===== */
.ud-team {
  padding-top: 120px;
  padding-bottom: 80px;
}

@media (max-width: 767px) {
  .ud-team {
    padding-top: 80px;
    padding-bottom: 40px;
  }
}

.ud-single-team {
  margin-bottom: 40px;
}

.ud-single-team .ud-team-image-wrapper {
  width: 170px;
  height: 170px;
  position: relative;
  margin: 0 auto 25px;
}

.ud-single-team .ud-team-image {
  border-radius: 50%;
}

.ud-single-team .ud-team-image img {
  width: 100%;
  border-radius: 50%;
}

.ud-single-team .shape {
  position: absolute;
  z-index: -1;
}

.ud-single-team .shape.shape-1 {
  top: 0;
  left: 0;
}

.ud-single-team .shape.shape-2 {
  bottom: 0;
  right: 0;
}

.ud-single-team .ud-team-info {
  text-align: center;
}

.ud-single-team .ud-team-info h3 {
  font-weight: 500;
  font-size: 18px;
  margin-bottom: 10px;
}

.ud-single-team .ud-team-info h4 {
  font-weight: 500;
  font-size: 12px;
  color: var(--body-color);
}

.ud-team-info a {
  padding: 5px 0;
  margin: 0;
}

.ud-single-team .ud-team-socials {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.ud-single-team .ud-team-socials a {
  width: 32px;
  height: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0 10px;
  color: #cdced6;
}

.ud-single-team .ud-team-socials a:hover {
  color: var(--primary-color);
}

/* ===== Testimonials CSS ===== */
.ud-testimonials {
  padding-top: 120px;
}

@media (max-width: 767px) {
  .ud-testimonials {
    padding-top: 80px;
  }
}

.ud-single-testimonial {
  padding: 30px;
  border-radius: 3px;
  border: 1px solid #ddd;
  background: var(--white);
  margin-bottom: 50px;
}

.ud-single-testimonial .ud-testimonial-ratings {
  margin-bottom: 10px;
}

.ud-single-testimonial .ud-testimonial-ratings i {
  color: #fbb040;
}

.ud-single-testimonial .ud-testimonial-content {
  margin-bottom: 25px;
}

.ud-single-testimonial .ud-testimonial-content p {
  font-size: 16px;
  line-height: 30px;
  color: #637381;
}

.ud-single-testimonial .ud-testimonial-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.ud-single-testimonial .ud-testimonial-info .ud-testimonial-meta h3 {
  font-weight: 600;
  font-size: 14px;
}

.ud-single-testimonial .ud-testimonial-info .ud-testimonial-meta p {
  font-size: 12px;
  color: #969696;
}

/* ===== Contact CSS ===== */
.ud-contact {
  padding: 120px 0;
  position: relative;
}

@media (max-width: 767px) {
  .ud-contact {
    padding: 80px 0;
  }
}

.ud-contact::after {
  content: "";
  position: absolute;
  z-index: -1;
  width: 100%;
  top: 0;
  left: 0;
  height: 50%;
  background: #f3f4fe;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .ud-contact::after {
    height: 45%;
  }
}

.ud-contact-title span {
  color: var(--heading-color);
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 20px;
}

.ud-contact-title h2 {
  font-weight: 600;
  font-size: 35px;
  line-height: 41px;
}

.ud-contact-info-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .ud-contact-info-wrapper {
    margin-bottom: 50px;
  }
}

.ud-contact-info-wrapper .ud-single-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  max-width: 100%;
  width: 330px;
  margin-bottom: 30px;
}

.ud-contact-info-wrapper .ud-info-icon {
  font-size: 32px;
  color: var(--primary-color);
  margin-right: 24px;
}

.ud-contact-info-wrapper .ud-info-meta h3 {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 5px;
}

.ud-contact-info-wrapper .ud-info-meta p {
  font-size: 15px;
  line-height: 24px;
}

.ud-contact-form-wrapper {
  -webkit-box-shadow: 0 4px 28px rgba(0, 0, 0, 0.05);
          box-shadow: 0 4px 28px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  background: var(--white);
  padding: 60px;
}

@media (max-width: 767px) {
  .ud-contact-form-wrapper {
    padding: 40px 30px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px) {
  .ud-contact-form-wrapper {
    padding: 50px 40px;
  }
}

.ud-contact-form-wrapper .ud-contact-form-title {
  font-style: normal;
  font-weight: 600;
  font-size: 28px;
  margin-bottom: 30px;
}

@media (max-width: 767px) {
  .ud-contact-form-wrapper .ud-contact-form-title {
    font-size: 24px;
  }
}

.ud-contact-form-wrapper .ud-form-group {
  margin-bottom: 25px;
}

.ud-contact-form-wrapper .ud-form-group label {
  display: block;
  font-weight: normal;
  font-size: 12px;
}

.ud-contact-form-wrapper .ud-form-group input,
.ud-contact-form-wrapper .ud-form-group textarea {
  display: block;
  width: 100%;
  padding: 15px 0;
  border: none;
  outline: none;
  border-bottom: 1px solid #f1f1f1;
  resize: none;
}

.ud-contact-form-wrapper .ud-form-group input:focus,
.ud-contact-form-wrapper .ud-form-group textarea:focus {
  border-color: var(--primary-color);
}

/* ===== Footer CSS ===== */
.ud-footer {
  background: var(--heading-color);
  position: relative;
  z-index: 1;
  overflow: hidden;
  width: 100%;
}

.ud-footer .shape {
  position: absolute;
  z-index: -1;
}

.ud-footer .shape.shape-1 {
  top: 0;
  left: 0;
}

.ud-footer .shape.shape-2 {
  top: 0;
  right: 0;
}

.ud-footer .shape.shape-3 {
  bottom: 0;
  right: 0;
}

.ud-widget .ud-footer-logo img {
  width: 100%;
}

.ud-widget .ud-widget-socials a {
  font-size: 16px;
  color: #dddddd;
  margin-right: 20px;
}

.ud-widget .ud-widget-socials a:hover {
  color: var(--primary-color);
}

.ud-widget .ud-widget-links a {
  display: inline-block;
  margin-bottom: 12px;
  font-size: 15px;
  line-height: 30px;
  color: #f3f4fe;
}

.ud-widget .ud-widget-links a:hover {
  color: var(--primary-color);
  padding-left: 10px;
}

.ud-widget .ud-widget-brands a {
  display: block;
  max-width: 120px;
  margin-bottom: 20px;
  margin-right: 20px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .ud-widget .ud-widget-brands a {
    max-width: 100px;
  }
}

.ud-footer-bottom {
  padding: 35px 0;
  border-top: 1px solid rgba(136, 144, 164, 0.43);
}

.ud-footer-bottom .ud-footer-bottom-left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 767px) {
  .ud-footer-bottom .ud-footer-bottom-left {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.ud-footer-bottom .ud-footer-bottom-left a {
  font-weight: normal;
  font-size: 15px;
  color: #f3f4fe;
  margin-right: 30px;
  display: inline-block;
}

@media (max-width: 767px) {
  .ud-footer-bottom .ud-footer-bottom-left a {
    margin: 0 8px 8px;
  }
}

.ud-footer-bottom .ud-footer-bottom-left a:hover {
  color: var(--primary-color);
}

.ud-footer-bottom .ud-footer-bottom-right {
  text-align: right;
  font-weight: normal;
  font-size: 15px;
  color: #f3f4fe;
}

.ud-footer-bottom .ud-footer-bottom-right a {
  color: var(--primary-color);
}

.ud-footer-bottom .ud-footer-bottom-right a:hover {
  text-decoration: underline;
}

@media (max-width: 767px) {
  .ud-footer-bottom .ud-footer-bottom-right {
    text-align: center;
    margin-top: 10px;
  }
}

.ud-logo-text {
    font-size: 18px;
}
