/*! tailwindcss v4.1.13 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-content:"";--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-space-y-reverse:0;--tw-border-style:solid;--tw-divide-y-reverse:0;--tw-font-weight:initial;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-x-reverse:0;--tw-divide-x-reverse:0;--tw-leading:initial;--tw-tracking:initial;--tw-outline-style:solid;--tw-duration:initial;--tw-ease:initial;--tw-text-shadow-color:initial;--tw-text-shadow-alpha:100%;--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-50:oklch(97.1% .013 17.38);--color-red-400:oklch(70.4% .191 22.216);--color-red-500:oklch(63.7% .237 25.331);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-amber-600:oklch(66.6% .179 58.318);--color-amber-700:oklch(55.5% .163 48.998);--color-amber-800:oklch(47.3% .137 46.201);--color-yellow-50:oklch(98.7% .026 102.212);--color-yellow-400:oklch(85.2% .199 91.936);--color-yellow-500:oklch(79.5% .184 86.047);--color-yellow-600:oklch(68.1% .162 75.834);--color-yellow-800:oklch(47.6% .114 61.907);--color-green-50:oklch(98.2% .018 155.826);--color-green-400:oklch(79.2% .209 151.711);--color-green-500:oklch(72.3% .219 149.579);--color-green-600:oklch(62.7% .194 149.214);--color-green-700:oklch(52.7% .154 150.069);--color-green-800:oklch(44.8% .119 151.328);--color-blue-50:oklch(97% .014 254.604);--color-blue-400:oklch(70.7% .165 254.624);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-blue-800:oklch(42.4% .199 265.638);--color-indigo-50:oklch(96.2% .018 272.314);--color-indigo-400:oklch(67.3% .182 276.935);--color-indigo-700:oklch(45.7% .24 277.023);--color-purple-50:oklch(97.7% .014 308.299);--color-purple-400:oklch(71.4% .203 305.504);--color-purple-700:oklch(49.6% .265 301.924);--color-pink-50:oklch(97.1% .014 343.198);--color-pink-400:oklch(71.8% .202 349.761);--color-pink-700:oklch(52.5% .223 3.958);--color-gray-50:oklch(98.5% .002 247.839);--color-gray-100:oklch(96.7% .003 264.542);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-300:oklch(87.2% .01 258.338);--color-gray-400:oklch(70.7% .022 261.325);--color-gray-500:oklch(55.1% .027 264.364);--color-gray-600:oklch(44.6% .03 256.802);--color-gray-700:oklch(37.3% .034 259.733);--color-gray-800:oklch(27.8% .033 256.848);--color-gray-900:oklch(21% .034 264.665);--color-zinc-700:oklch(37% .013 285.805);--color-zinc-800:oklch(27.4% .006 286.033);--color-neutral-500:oklch(55.6% 0 0);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-xs:20rem;--container-md:28rem;--container-lg:32rem;--container-2xl:42rem;--container-7xl:80rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height: 1.5 ;--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--text-3xl:1.875rem;--text-3xl--line-height: 1.2 ;--text-5xl:3rem;--text-5xl--line-height:1;--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--font-weight-black:900;--tracking-tight:-.025em;--tracking-wide:.025em;--tracking-wider:.05em;--radius-xs:.125rem;--radius-sm:.25rem;--radius-md:.375rem;--radius-lg:.5rem;--radius-xl:.75rem;--ease-in:cubic-bezier(.4,0,1,1);--ease-out:cubic-bezier(0,0,.2,1);--ease-in-out:cubic-bezier(.4,0,.2,1);--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-primary:var(--color-primary-600);--color-primary-100:oklch(93.2% .032 255.585);--color-primary-200:oklch(88.2% .059 254.128);--color-primary-300:oklch(80.9% .105 251.813);--color-primary-400:oklch(70.7% .165 254.624);--color-primary-500:oklch(62.3% .214 259.815);--color-primary-600:oklch(54.6% .245 262.881);--color-primary-700:oklch(48.8% .243 264.376);--color-primary-800:oklch(42.4% .199 265.638);--color-primary-900:oklch(37.9% .146 265.522);--color-primary-950:oklch(28.2% .091 267.935)}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}::-webkit-calendar-picker-indicator{line-height:1}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}*{font-family:IBM Plex Sans,sans-serif}}@layer components{.alert-window .badge-blue{height:calc(var(--spacing)*8);width:calc(var(--spacing)*8);border-radius:var(--radius-lg);background-color:var(--color-blue-50);color:var(--color-blue-700);flex-shrink:0;justify-content:center;align-items:center;display:inline-flex}.alert-window .badge-blue:where(.dark,.dark *){background-color:#54a2ff1a}@supports (color:color-mix(in lab,red,red)){.alert-window .badge-blue:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-blue-400)10%,transparent)}}.alert-window .badge-blue:where(.dark,.dark *){color:var(--color-blue-400)}.alert-window .badge-green{height:calc(var(--spacing)*8);width:calc(var(--spacing)*8);border-radius:var(--radius-lg);background-color:var(--color-green-50);color:var(--color-green-700);flex-shrink:0;justify-content:center;align-items:center;display:inline-flex}.alert-window .badge-green:where(.dark,.dark *){background-color:#05df721a}@supports (color:color-mix(in lab,red,red)){.alert-window .badge-green:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-green-400)10%,transparent)}}.alert-window .badge-green:where(.dark,.dark *){color:var(--color-green-400)}.alert-window .badge-yellow{height:calc(var(--spacing)*8);width:calc(var(--spacing)*8);border-radius:var(--radius-lg);background-color:var(--color-yellow-50);color:var(--color-yellow-800);flex-shrink:0;justify-content:center;align-items:center;display:inline-flex}.alert-window .badge-yellow:where(.dark,.dark *){background-color:#fac8001a}@supports (color:color-mix(in lab,red,red)){.alert-window .badge-yellow:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-yellow-400)10%,transparent)}}.alert-window .badge-yellow:where(.dark,.dark *){color:var(--color-yellow-500)}.alert-window .badge-red{height:calc(var(--spacing)*8);width:calc(var(--spacing)*8);border-radius:var(--radius-lg);background-color:var(--color-red-50);color:var(--color-red-700);flex-shrink:0;justify-content:center;align-items:center;display:inline-flex}.alert-window .badge-red:where(.dark,.dark *){background-color:#ff65681a}@supports (color:color-mix(in lab,red,red)){.alert-window .badge-red:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-red-400)10%,transparent)}}.alert-window .badge-red:where(.dark,.dark *){color:var(--color-red-400)}}@layer utilities{.sidebar-menu{row-gap:calc(var(--spacing)*5);background-color:var(--color-primary-600);padding-inline:calc(var(--spacing)*6);padding-bottom:calc(var(--spacing)*4);flex-direction:column;flex-grow:1;display:flex;position:relative;overflow-y:auto}.sidebar-menu:where(.dark,.dark *){background-color:var(--color-primary-800)}.sidebar-menu:where(.dark,.dark *):after{pointer-events:none;inset-block:calc(var(--spacing)*0);right:calc(var(--spacing)*0);content:var(--tw-content);background-color:#ffffff1a;width:1px;position:absolute}@supports (color:color-mix(in lab,red,red)){.sidebar-menu:where(.dark,.dark *):after{background-color:color-mix(in oklab,var(--color-white)10%,transparent)}}.desktop-sidebar{display:none}@media (min-width:64rem){.desktop-sidebar{inset-block:calc(var(--spacing)*0);z-index:50;width:calc(var(--spacing)*72);flex-direction:column;display:flex;position:fixed}}.sr-only{clip-path:inset(50%);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.sticky{position:sticky}.-inset-1\.5{inset:calc(var(--spacing)*-1.5)}.-inset-2\.5{inset:calc(var(--spacing)*-2.5)}.inset-0{inset:calc(var(--spacing)*0)}.top-0{top:calc(var(--spacing)*0)}.right-6{right:calc(var(--spacing)*6)}.left-6{left:calc(var(--spacing)*6)}.left-full{left:100%}.z-40{z-index:40}.z-\[1000\]{z-index:1000}.col-span-1{grid-column:span 1/span 1}.col-start-1{grid-column-start:1}.row-start-1{grid-row-start:1}.container{width:100%}@media (min-width:40rem){.container{max-width:40rem}}@media (min-width:48rem){.container{max-width:48rem}}@media (min-width:64rem){.container{max-width:64rem}}@media (min-width:80rem){.container{max-width:80rem}}@media (min-width:96rem){.container{max-width:96rem}}.-m-2\.5{margin:calc(var(--spacing)*-2.5)}.m-0{margin:calc(var(--spacing)*0)}.alert-close{margin-inline:calc(var(--spacing)*-1.5);margin-block:calc(var(--spacing)*-1.5);height:calc(var(--spacing)*8);width:calc(var(--spacing)*8);border-radius:var(--radius-lg);padding:calc(var(--spacing)*1.5);color:var(--color-gray-400);justify-content:center;align-items:center;margin-inline-start:auto;display:inline-flex}@media (hover:hover){.alert-close:hover{background-color:var(--color-gray-100);color:var(--color-gray-900)}}.alert-close:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-gray-300)}@media (hover:hover){.alert-close:where(.dark,.dark *):hover{background-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){.alert-close:where(.dark,.dark *):hover{background-color:color-mix(in oklab,var(--color-white)10%,transparent)}}.alert-close:where(.dark,.dark *):hover{color:var(--color-white)}}.-mx-2{margin-inline:calc(var(--spacing)*-2)}.-mx-4{margin-inline:calc(var(--spacing)*-4)}.mx-1\.5{margin-inline:calc(var(--spacing)*1.5)}.mx-4{margin-inline:calc(var(--spacing)*4)}.mx-auto{margin-inline:auto}.-my-2{margin-block:calc(var(--spacing)*-2)}.-my-3{margin-block:calc(var(--spacing)*-3)}.my-2{margin-block:calc(var(--spacing)*2)}.my-4{margin-block:calc(var(--spacing)*4)}.my-6{margin-block:calc(var(--spacing)*6)}.ms-2{margin-inline-start:calc(var(--spacing)*2)}.ms-auto{margin-inline-start:auto}.me-2{margin-inline-end:calc(var(--spacing)*2)}.form-wrapper{margin-top:calc(var(--spacing)*4)}:where(.form-wrapper>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))}.form-wrapper{border-bottom-style:var(--tw-border-style);border-color:#1018281a;border-bottom-width:1px}@supports (color:color-mix(in lab,red,red)){.form-wrapper{border-color:color-mix(in oklab,var(--color-gray-900)10%,transparent)}}.form-wrapper{padding-bottom:calc(var(--spacing)*12)}@media (min-width:40rem){:where(.form-wrapper>:not(:last-child)){--tw-space-y-reverse:0;--tw-divide-y-reverse:0;border-bottom-style:var(--tw-border-style);border-top-style:var(--tw-border-style);border-top-width:calc(1px*var(--tw-divide-y-reverse));border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)));border-color:#1018281a;margin-block-start:calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))}@supports (color:color-mix(in lab,red,red)){:where(.form-wrapper>:not(:last-child)){border-color:color-mix(in oklab,var(--color-gray-900)10%,transparent)}}.form-wrapper{border-top-style:var(--tw-border-style);border-top-width:1px;border-top-color:#1018281a}@supports (color:color-mix(in lab,red,red)){.form-wrapper{border-top-color:color-mix(in oklab,var(--color-gray-900)10%,transparent)}}.form-wrapper{padding-bottom:calc(var(--spacing)*0)}}.form-wrapper:where(.dark,.dark *){border-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){.form-wrapper:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-white)10%,transparent)}}@media (min-width:40rem){:where(.form-wrapper:where(.dark,.dark *)>:not(:last-child)){border-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){:where(.form-wrapper:where(.dark,.dark *)>:not(:last-child)){border-color:color-mix(in oklab,var(--color-white)10%,transparent)}}.form-wrapper:where(.dark,.dark *){border-top-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){.form-wrapper:where(.dark,.dark *){border-top-color:color-mix(in oklab,var(--color-white)10%,transparent)}}}.-mt-px{margin-top:-1px}.mt-1{margin-top:calc(var(--spacing)*1)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-6{margin-top:calc(var(--spacing)*6)}.mt-8{margin-top:calc(var(--spacing)*8)}.mt-10{margin-top:calc(var(--spacing)*10)}.mt-auto{margin-top:auto}.-mr-px{margin-right:-1px}.mr-1{margin-right:calc(var(--spacing)*1)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-16{margin-right:calc(var(--spacing)*16)}.mb-0{margin-bottom:calc(var(--spacing)*0)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-5{margin-bottom:calc(var(--spacing)*5)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.-ml-px{margin-left:-1px}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-2{margin-left:calc(var(--spacing)*2)}.ml-3{margin-left:calc(var(--spacing)*3)}.ml-4{margin-left:calc(var(--spacing)*4)}.ml-6{margin-left:calc(var(--spacing)*6)}.ml-auto{margin-left:auto}.alert-window{border-radius:var(--radius-lg);background-color:var(--color-white);width:100%;padding:calc(var(--spacing)*4);color:var(--color-gray-500);--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);align-items:center;display:flex}.alert-window:where(.dark,.dark *){background-color:var(--color-zinc-700);color:var(--color-gray-300)}@media (min-width:40rem){.form-row{align-items:flex-start;gap:calc(var(--spacing)*4);padding-block:calc(var(--spacing)*6);grid-template-columns:repeat(3,minmax(0,1fr));display:grid}}.sidebar-item{align-items:center;column-gap:calc(var(--spacing)*3);border-radius:var(--radius-md);padding:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:calc(var(--spacing)*6);--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold);color:var(--color-primary-200);display:flex}@media (hover:hover){.sidebar-item:hover{background-color:var(--color-primary-700);color:var(--color-white)}}.sidebar-item:where(.dark,.dark *){color:var(--color-primary-100)}@media (hover:hover){.sidebar-item:where(.dark,.dark *):hover{background-color:#16245640}@supports (color:color-mix(in lab,red,red)){.sidebar-item:where(.dark,.dark *):hover{background-color:color-mix(in oklab,var(--color-primary-950)25%,transparent)}}}.sidebar-item i{color:var(--color-primary-200)}.sidebar-item i:where(.dark,.dark *){color:var(--color-primary-100)}.sidebar-item.active{background-color:var(--color-primary-700)!important;color:var(--color-white)!important}.sidebar-item.active:where(.dark,.dark *){background-color:#16245640!important}@supports (color:color-mix(in lab,red,red)){.sidebar-item.active:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-primary-950)25%,transparent)!important}}.block{display:block}.contents{display:contents}.flex{display:flex}.flow-root{display:flow-root}.grid{display:grid}.hidden{display:none}.inline-block{display:inline-block}.inline-flex{display:inline-flex}.table{display:table}.size-5{width:calc(var(--spacing)*5);height:calc(var(--spacing)*5)}.size-6{width:calc(var(--spacing)*6);height:calc(var(--spacing)*6)}.size-12{width:calc(var(--spacing)*12);height:calc(var(--spacing)*12)}.h-1{height:calc(var(--spacing)*1)}.h-2{height:calc(var(--spacing)*2)}.h-3{height:calc(var(--spacing)*3)}.h-6{height:calc(var(--spacing)*6)}.h-7{height:calc(var(--spacing)*7)}.h-8{height:calc(var(--spacing)*8)}.h-10{height:calc(var(--spacing)*10)}.h-12{height:calc(var(--spacing)*12)}.h-16{height:calc(var(--spacing)*16)}.h-full{height:100%}.h-full\!{height:100%!important}.w-0{width:calc(var(--spacing)*0)}.w-1\/2{width:50%}.w-1\/3{width:33.3333%}.w-1\/5{width:20%}.w-1\/6{width:16.6667%}.w-2{width:calc(var(--spacing)*2)}.w-3{width:calc(var(--spacing)*3)}.w-4{width:calc(var(--spacing)*4)}.w-6{width:calc(var(--spacing)*6)}.w-8{width:calc(var(--spacing)*8)}.w-16{width:calc(var(--spacing)*16)}.w-32{width:calc(var(--spacing)*32)}.w-34{width:calc(var(--spacing)*34)}.w-44{width:calc(var(--spacing)*44)}.w-46{width:calc(var(--spacing)*46)}.w-50{width:calc(var(--spacing)*50)}.w-75{width:calc(var(--spacing)*75)}.w-100{width:calc(var(--spacing)*100)}.w-full{width:100%}.max-w-2xl{max-width:var(--container-2xl)}.max-w-7xl{max-width:var(--container-7xl)}.max-w-lg{max-width:var(--container-lg)}.max-w-md{max-width:var(--container-md)}.max-w-xs{max-width:var(--container-xs)}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-full{min-width:100%}.flex-1{flex:1}.shrink{flex-shrink:1}.shrink-0{flex-shrink:0}.grow{flex-grow:1}.border-collapse{border-collapse:collapse}.origin-top-right{transform-origin:100% 0}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.cursor-pointer{cursor:pointer}.list-inside{list-style-position:inside}.list-disc{list-style-type:disc}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.flex-col{flex-direction:column}.items-center{align-items:center}.items-start{align-items:flex-start}.justify-around{justify-content:space-around}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start{justify-content:flex-start}.gap-2{gap:calc(var(--spacing)*2)}.gap-4{gap:calc(var(--spacing)*4)}.gap-6{gap:calc(var(--spacing)*6)}.gap-24{gap:calc(var(--spacing)*24)}:where(.space-y-0\.5>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*.5)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*.5)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-8>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-12>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*12)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*12)*calc(1 - var(--tw-space-y-reverse)))}.gap-x-3{column-gap:calc(var(--spacing)*3)}.gap-x-4{column-gap:calc(var(--spacing)*4)}.gap-x-6{column-gap:calc(var(--spacing)*6)}.gap-x-8{column-gap:calc(var(--spacing)*8)}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-3>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*3)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-4>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*4)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-6>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*6)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-x-reverse)))}.gap-y-7{row-gap:calc(var(--spacing)*7)}.gap-y-8{row-gap:calc(var(--spacing)*8)}.gap-y-16{row-gap:calc(var(--spacing)*16)}:where(.divide-x>:not(:last-child)){--tw-divide-x-reverse:0;border-inline-style:var(--tw-border-style);border-inline-start-width:calc(1px*var(--tw-divide-x-reverse));border-inline-end-width:calc(1px*calc(1 - var(--tw-divide-x-reverse)))}:where(.divide-y>:not(:last-child)){--tw-divide-y-reverse:0;border-bottom-style:var(--tw-border-style);border-top-style:var(--tw-border-style);border-top-width:calc(1px*var(--tw-divide-y-reverse));border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))}:where(.divide-gray-100>:not(:last-child)){border-color:var(--color-gray-100)}:where(.divide-gray-200>:not(:last-child)){border-color:var(--color-gray-200)}:where(.divide-gray-300>:not(:last-child)){border-color:var(--color-gray-300)}:where(.divide-gray-900\/10>:not(:last-child)){border-color:#1018281a}@supports (color:color-mix(in lab,red,red)){:where(.divide-gray-900\/10>:not(:last-child)){border-color:color-mix(in oklab,var(--color-gray-900)10%,transparent)}}.self-center{align-self:center}.self-stretch{align-self:stretch}.justify-self-end{justify-self:flex-end}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius-lg)}.rounded-md{border-radius:var(--radius-md)}.rounded-sm{border-radius:var(--radius-sm)}.rounded-xl{border-radius:var(--radius-xl)}.rounded-xs{border-radius:var(--radius-xs)}.rounded-s-lg{border-start-start-radius:var(--radius-lg);border-end-start-radius:var(--radius-lg)}.rounded-e-lg{border-start-end-radius:var(--radius-lg);border-end-end-radius:var(--radius-lg)}.rounded-b-lg{border-bottom-right-radius:var(--radius-lg);border-bottom-left-radius:var(--radius-lg)}.rounded-br-lg{border-bottom-right-radius:var(--radius-lg)}.rounded-bl-lg{border-bottom-left-radius:var(--radius-lg)}.border{border-style:var(--tw-border-style);border-width:1px}.border-0{border-style:var(--tw-border-style);border-width:0}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-dashed{--tw-border-style:dashed;border-style:dashed}.border-black{border-color:var(--color-black)}.border-gray-100{border-color:var(--color-gray-100)}.border-gray-200{border-color:var(--color-gray-200)}.border-gray-300{border-color:var(--color-gray-300)}.border-gray-900{border-color:var(--color-gray-900)}.border-gray-900\/5{border-color:#1018280d}@supports (color:color-mix(in lab,red,red)){.border-gray-900\/5{border-color:color-mix(in oklab,var(--color-gray-900)5%,transparent)}}.border-gray-900\/25{border-color:#10182840}@supports (color:color-mix(in lab,red,red)){.border-gray-900\/25{border-color:color-mix(in oklab,var(--color-gray-900)25%,transparent)}}.border-transparent{border-color:#0000}.border-yellow-400{border-color:var(--color-yellow-400)}.bg-amber-700{background-color:var(--color-amber-700)}.bg-black{background-color:var(--color-black)}.bg-blue-600{background-color:var(--color-blue-600)}.bg-blue-700{background-color:var(--color-blue-700)}.bg-blue-800{background-color:var(--color-blue-800)}.bg-gray-50{background-color:var(--color-gray-50)}.bg-gray-100{background-color:var(--color-gray-100)}.bg-gray-300{background-color:var(--color-gray-300)}.bg-gray-700{background-color:var(--color-gray-700)}.bg-gray-800{background-color:var(--color-gray-800)}.bg-gray-900\/80{background-color:#101828cc}@supports (color:color-mix(in lab,red,red)){.bg-gray-900\/80{background-color:color-mix(in oklab,var(--color-gray-900)80%,transparent)}}.bg-green-50{background-color:var(--color-green-50)}.bg-green-700{background-color:var(--color-green-700)}.bg-primary{background-color:var(--color-primary)}.bg-primary-800{background-color:var(--color-primary-800)}.bg-red-600{background-color:var(--color-red-600)}.bg-transparent{background-color:#0000}.bg-white{background-color:var(--color-white)}.p-0{padding:calc(var(--spacing)*0)}.p-2{padding:calc(var(--spacing)*2)}.p-2\.5{padding:calc(var(--spacing)*2.5)}.p-4{padding:calc(var(--spacing)*4)}.p-6{padding:calc(var(--spacing)*6)}.px-1{padding-inline:calc(var(--spacing)*1)}.px-1\.5{padding-inline:calc(var(--spacing)*1.5)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-5{padding-inline:calc(var(--spacing)*5)}.px-6{padding-inline:calc(var(--spacing)*6)}.py-0\.5{padding-block:calc(var(--spacing)*.5)}.py-1{padding-block:calc(var(--spacing)*1)}.py-2{padding-block:calc(var(--spacing)*2)}.py-2\.5{padding-block:calc(var(--spacing)*2.5)}.py-3{padding-block:calc(var(--spacing)*3)}.py-3\.5{padding-block:calc(var(--spacing)*3.5)}.py-4{padding-block:calc(var(--spacing)*4)}.py-4\!{padding-block:calc(var(--spacing)*4)!important}.py-6{padding-block:calc(var(--spacing)*6)}.py-8{padding-block:calc(var(--spacing)*8)}.py-10{padding-block:calc(var(--spacing)*10)}.pt-2{padding-top:calc(var(--spacing)*2)}.pt-5{padding-top:calc(var(--spacing)*5)}.pr-3{padding-right:calc(var(--spacing)*3)}.pr-4{padding-right:calc(var(--spacing)*4)}.pr-5{padding-right:calc(var(--spacing)*5)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pb-6{padding-bottom:calc(var(--spacing)*6)}.pl-1{padding-left:calc(var(--spacing)*1)}.pl-3{padding-left:calc(var(--spacing)*3)}.pl-4{padding-left:calc(var(--spacing)*4)}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.align-middle{vertical-align:middle}.font-mono{font-family:var(--font-mono)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-base\/7{font-size:var(--text-base);line-height:calc(var(--spacing)*7)}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-sm\/6{font-size:var(--text-sm);line-height:calc(var(--spacing)*6)}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.text-\[6px\]{font-size:6px}.text-\[8px\]{font-size:8px}.leading-none{--tw-leading:1;line-height:1}.font-black{--tw-font-weight:var(--font-weight-black);font-weight:var(--font-weight-black)}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-wide{--tw-tracking:var(--tracking-wide);letter-spacing:var(--tracking-wide)}.tracking-wider{--tw-tracking:var(--tracking-wider);letter-spacing:var(--tracking-wider)}.whitespace-nowrap{white-space:nowrap}.text-black{color:var(--color-black)}.text-gray-300{color:var(--color-gray-300)}.text-gray-400{color:var(--color-gray-400)}.text-gray-500{color:var(--color-gray-500)}.text-gray-600{color:var(--color-gray-600)}.text-gray-700{color:var(--color-gray-700)}.text-gray-800{color:var(--color-gray-800)}.text-gray-900{color:var(--color-gray-900)}.text-green-500{color:var(--color-green-500)}.text-green-700{color:var(--color-green-700)}.text-indigo-700{color:var(--color-indigo-700)}.text-primary{color:var(--color-primary)}.text-primary-200{color:var(--color-primary-200)}.text-primary-600{color:var(--color-primary-600)}.text-red-600{color:var(--color-red-600)}.text-white{color:var(--color-white)}.text-yellow-400{color:var(--color-yellow-400)}.text-yellow-500{color:var(--color-yellow-500)}.opacity-60{opacity:.6}.shadow-2xs{--tw-shadow:0 1px var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xs{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.inset-ring{--tw-inset-ring-shadow:inset 0 0 0 1px var(--tw-inset-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.inset-ring-green-600\/20{--tw-inset-ring-color:#00a54433}@supports (color:color-mix(in lab,red,red)){.inset-ring-green-600\/20{--tw-inset-ring-color:color-mix(in oklab,var(--color-green-600)20%,transparent)}}.outline,.outline-1{outline-style:var(--tw-outline-style);outline-width:1px}.outline-gray-200{outline-color:var(--color-gray-200)}.outline-gray-900\/5{outline-color:#1018280d}@supports (color:color-mix(in lab,red,red)){.outline-gray-900\/5{outline-color:color-mix(in oklab,var(--color-gray-900)5%,transparent)}}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-discrete{transition-behavior:allow-discrete}.duration-300{--tw-duration:.3s;transition-duration:.3s}.ease-in-out{--tw-ease:var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.ease-linear{--tw-ease:linear;transition-timing-function:linear}.\[--anchor-gap\:--spacing\(0\.5\)\]{--anchor-gap:calc(var(--spacing)*.5)}.text-shadow-lg{text-shadow:0px 1px 2px var(--tw-text-shadow-color,#0000001a),0px 3px 2px var(--tw-text-shadow-color,#0000001a),0px 4px 8px var(--tw-text-shadow-color,#0000001a)}.not-in-aria-expanded\:hidden:not(:where([aria-expanded=true]) *){display:none}@media (hover:hover){.group-hover\:text-white:is(:where(.group):hover *){color:var(--color-white)}}.group-data-closed\/dialog-panel\:opacity-0:is(:where(.group\/dialog-panel)[data-closed] *){opacity:0}.backdrop\:bg-transparent::backdrop{background-color:#0000}.first\:pt-0:first-child{padding-top:calc(var(--spacing)*0)}.last\:pb-0:last-child{padding-bottom:calc(var(--spacing)*0)}.odd\:bg-white:nth-child(odd){background-color:var(--color-white)}.even\:bg-gray-200:nth-child(2n){background-color:var(--color-gray-200)}.focus-within\:outline-2:focus-within{outline-style:var(--tw-outline-style);outline-width:2px}.focus-within\:outline-offset-2:focus-within{outline-offset:2px}.focus-within\:outline-primary-600:focus-within{outline-color:var(--color-primary-600)}@media (hover:hover){.hover\:bg-amber-800:hover{background-color:var(--color-amber-800)}.hover\:bg-blue-800:hover{background-color:var(--color-blue-800)}.hover\:bg-gray-900:hover{background-color:var(--color-gray-900)}.hover\:bg-green-800:hover{background-color:var(--color-green-800)}.hover\:bg-primary-700:hover{background-color:var(--color-primary-700)}.hover\:bg-primary-900:hover{background-color:var(--color-primary-900)}.hover\:text-\[\#f55247\]:hover{color:#f55247}.hover\:text-gray-500:hover{color:var(--color-gray-500)}.hover\:text-gray-600:hover{color:var(--color-gray-600)}.hover\:text-gray-900:hover{color:var(--color-gray-900)}.hover\:text-primary:hover{color:var(--color-primary)}.hover\:text-primary-500:hover{color:var(--color-primary-500)}.hover\:text-primary-900:hover{color:var(--color-primary-900)}.hover\:text-white:hover{color:var(--color-white)}}.focus\:z-10:focus{z-index:10}.focus\:bg-gray-50:focus{background-color:var(--color-gray-50)}.focus\:outline-hidden:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus\:outline-hidden:focus{outline-offset:2px;outline:2px solid #0000}}.focus\:outline-none:focus{--tw-outline-style:none;outline-style:none}:where([aria-expanded=true]) .in-aria-expanded\:hidden{display:none}.data-closed\:-translate-x-full[data-closed]{--tw-translate-x:-100%;translate:var(--tw-translate-x)var(--tw-translate-y)}.data-closed\:scale-95[data-closed]{--tw-scale-x:95%;--tw-scale-y:95%;--tw-scale-z:95%;scale:var(--tw-scale-x)var(--tw-scale-y)}.data-closed\:transform[data-closed]{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.data-closed\:opacity-0[data-closed]{opacity:0}.data-enter\:duration-100[data-enter]{--tw-duration:.1s;transition-duration:.1s}.data-enter\:ease-out[data-enter]{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}.data-leave\:duration-75[data-leave]{--tw-duration:75ms;transition-duration:75ms}.data-leave\:ease-in[data-leave]{--tw-ease:var(--ease-in);transition-timing-function:var(--ease-in)}@media (min-width:40rem){.sm\:col-span-2{grid-column:span 2/span 2}.sm\:-mx-6{margin-inline:calc(var(--spacing)*-6)}.sm\:mt-0{margin-top:calc(var(--spacing)*0)}.sm\:ml-16{margin-left:calc(var(--spacing)*16)}.sm\:ml-64{margin-left:calc(var(--spacing)*64)}.sm\:block{display:block}.sm\:flex{display:flex}.sm\:grid{display:grid}.sm\:hidden{display:none}.sm\:w-1\/3{width:33.3333%}.sm\:w-2\/3{width:66.6667%}.sm\:max-w-md{max-width:var(--container-md)}.sm\:max-w-xs{max-width:var(--container-xs)}.sm\:flex-auto{flex:auto}.sm\:flex-none{flex:none}.sm\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.sm\:items-center{align-items:center}.sm\:gap-4{gap:calc(var(--spacing)*4)}:where(.sm\:space-y-16>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*16)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*16)*calc(1 - var(--tw-space-y-reverse)))}.sm\:gap-x-6{column-gap:calc(var(--spacing)*6)}.sm\:rounded-lg{border-radius:var(--radius-lg)}.sm\:p-8{padding:calc(var(--spacing)*8)}.sm\:px-0{padding-inline:calc(var(--spacing)*0)}.sm\:px-6{padding-inline:calc(var(--spacing)*6)}.sm\:py-3{padding-block:calc(var(--spacing)*3)}.sm\:pr-0{padding-right:calc(var(--spacing)*0)}.sm\:pl-0{padding-left:calc(var(--spacing)*0)}.sm\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}}@media (min-width:48rem){.md\:mt-0{margin-top:calc(var(--spacing)*0)}.md\:h-screen{height:100vh}.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}:where(.md\:space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}}@media (min-width:64rem){.lg\:right-1\/3{right:33.3333%}.lg\:left-1\/3{left:33.3333%}.lg\:-mx-8{margin-inline:calc(var(--spacing)*-8)}.lg\:block{display:block}.lg\:flex{display:flex}.lg\:hidden{display:none}.lg\:h-6{height:calc(var(--spacing)*6)}.lg\:w-96{width:calc(var(--spacing)*96)}.lg\:w-px{width:1px}.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.lg\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.lg\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.lg\:items-center{align-items:center}.lg\:gap-x-6{column-gap:calc(var(--spacing)*6)}.lg\:bg-gray-900\/10{background-color:#1018281a}@supports (color:color-mix(in lab,red,red)){.lg\:bg-gray-900\/10{background-color:color-mix(in oklab,var(--color-gray-900)10%,transparent)}}.lg\:px-8{padding-inline:calc(var(--spacing)*8)}.lg\:py-0{padding-block:calc(var(--spacing)*0)}.lg\:pl-72{padding-left:calc(var(--spacing)*72)}}@media (min-width:80rem){.xl\:gap-x-8{column-gap:calc(var(--spacing)*8)}.xl\:p-0{padding:calc(var(--spacing)*0)}}.rtl\:text-right:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:right}.dark\:block:where(.dark,.dark *){display:block}.dark\:hidden:where(.dark,.dark *){display:none}:where(.dark\:divide-white\/5:where(.dark,.dark *)>:not(:last-child)){border-color:#ffffff0d}@supports (color:color-mix(in lab,red,red)){:where(.dark\:divide-white\/5:where(.dark,.dark *)>:not(:last-child)){border-color:color-mix(in oklab,var(--color-white)5%,transparent)}}:where(.dark\:divide-white\/10:where(.dark,.dark *)>:not(:last-child)){border-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){:where(.dark\:divide-white\/10:where(.dark,.dark *)>:not(:last-child)){border-color:color-mix(in oklab,var(--color-white)10%,transparent)}}:where(.dark\:divide-white\/15:where(.dark,.dark *)>:not(:last-child)){border-color:#ffffff26}@supports (color:color-mix(in lab,red,red)){:where(.dark\:divide-white\/15:where(.dark,.dark *)>:not(:last-child)){border-color:color-mix(in oklab,var(--color-white)15%,transparent)}}.dark\:border:where(.dark,.dark *){border-style:var(--tw-border-style);border-width:1px}.dark\:border-gray-500:where(.dark,.dark *){border-color:var(--color-gray-500)}.dark\:border-gray-700:where(.dark,.dark *){border-color:var(--color-gray-700)}.dark\:border-white:where(.dark,.dark *){border-color:var(--color-white)}.dark\:border-white\/10:where(.dark,.dark *){border-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){.dark\:border-white\/10:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-white)10%,transparent)}}.dark\:border-white\/25:where(.dark,.dark *){border-color:#ffffff40}@supports (color:color-mix(in lab,red,red)){.dark\:border-white\/25:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-white)25%,transparent)}}.dark\:bg-\[\#212121\]:where(.dark,.dark *){background-color:#212121}.dark\:bg-amber-600:where(.dark,.dark *){background-color:var(--color-amber-600)}.dark\:bg-blue-600:where(.dark,.dark *){background-color:var(--color-blue-600)}.dark\:bg-gray-50:where(.dark,.dark *){background-color:var(--color-gray-50)}.dark\:bg-gray-800\/50:where(.dark,.dark *){background-color:#1e293980}@supports (color:color-mix(in lab,red,red)){.dark\:bg-gray-800\/50:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-gray-800)50%,transparent)}}.dark\:bg-green-500\/10:where(.dark,.dark *){background-color:#00c7581a}@supports (color:color-mix(in lab,red,red)){.dark\:bg-green-500\/10:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-green-500)10%,transparent)}}.dark\:bg-green-600:where(.dark,.dark *){background-color:var(--color-green-600)}.dark\:bg-transparent:where(.dark,.dark *){background-color:#0000}.dark\:bg-zinc-800:where(.dark,.dark *){background-color:var(--color-zinc-800)}.dark\:text-gray-100:where(.dark,.dark *){color:var(--color-gray-100)}.dark\:text-gray-200:where(.dark,.dark *){color:var(--color-gray-200)}.dark\:text-gray-300:where(.dark,.dark *){color:var(--color-gray-300)}.dark\:text-gray-400:where(.dark,.dark *){color:var(--color-gray-400)}.dark\:text-gray-500:where(.dark,.dark *){color:var(--color-gray-500)}.dark\:text-gray-600:where(.dark,.dark *){color:var(--color-gray-600)}.dark\:text-green-500:where(.dark,.dark *){color:var(--color-green-500)}.dark\:text-primary-100:where(.dark,.dark *){color:var(--color-primary-100)}.dark\:text-primary-400:where(.dark,.dark *){color:var(--color-primary-400)}.dark\:text-white:where(.dark,.dark *){color:var(--color-white)}.dark\:shadow-none:where(.dark,.dark *){--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.dark\:inset-ring-green-500\/10:where(.dark,.dark *){--tw-inset-ring-color:#00c7581a}@supports (color:color-mix(in lab,red,red)){.dark\:inset-ring-green-500\/10:where(.dark,.dark *){--tw-inset-ring-color:color-mix(in oklab,var(--color-green-500)10%,transparent)}}.dark\:outline:where(.dark,.dark *){outline-style:var(--tw-outline-style);outline-width:1px}.dark\:-outline-offset-1:where(.dark,.dark *){outline-offset:-1px}.dark\:outline-white\/10:where(.dark,.dark *){outline-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){.dark\:outline-white\/10:where(.dark,.dark *){outline-color:color-mix(in oklab,var(--color-white)10%,transparent)}}.dark\:odd\:bg-gray-900:where(.dark,.dark *):nth-child(odd){background-color:var(--color-gray-900)}.dark\:even\:bg-gray-800:where(.dark,.dark *):nth-child(2n){background-color:var(--color-gray-800)}.dark\:focus-within\:outline-primary-500:where(.dark,.dark *):focus-within{outline-color:var(--color-primary-500)}@media (hover:hover){.dark\:hover\:bg-amber-700:where(.dark,.dark *):hover{background-color:var(--color-amber-700)}.dark\:hover\:bg-blue-700:where(.dark,.dark *):hover{background-color:var(--color-blue-700)}.dark\:hover\:bg-gray-700:where(.dark,.dark *):hover{background-color:var(--color-gray-700)}.dark\:hover\:bg-green-700:where(.dark,.dark *):hover{background-color:var(--color-green-700)}.dark\:hover\:bg-primary-950\/25:where(.dark,.dark *):hover{background-color:#16245640}@supports (color:color-mix(in lab,red,red)){.dark\:hover\:bg-primary-950\/25:where(.dark,.dark *):hover{background-color:color-mix(in oklab,var(--color-primary-950)25%,transparent)}}.dark\:hover\:text-gray-300:where(.dark,.dark *):hover{color:var(--color-gray-300)}.dark\:hover\:text-primary-300:where(.dark,.dark *):hover{color:var(--color-primary-300)}.dark\:hover\:text-white:where(.dark,.dark *):hover{color:var(--color-white)}}.dark\:focus\:bg-white\/5:where(.dark,.dark *):focus{background-color:#ffffff0d}@supports (color:color-mix(in lab,red,red)){.dark\:focus\:bg-white\/5:where(.dark,.dark *):focus{background-color:color-mix(in oklab,var(--color-white)5%,transparent)}}@media (min-width:64rem){.dark\:lg\:bg-gray-100\/10:where(.dark,.dark *){background-color:#f3f4f61a}@supports (color:color-mix(in lab,red,red)){.dark\:lg\:bg-gray-100\/10:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-gray-100)10%,transparent)}}}}.badge-red{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);--tw-inset-ring-shadow:inset 0 0 0 1px var(--tw-inset-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-red-50);color:var(--color-red-700);--tw-inset-ring-color:#e400141a;align-items:center;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.badge-red{--tw-inset-ring-color:color-mix(in oklab,var(--color-red-600)10%,transparent)}}.badge-red:where(.dark,.dark *){background-color:#ff65681a}@supports (color:color-mix(in lab,red,red)){.badge-red:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-red-400)10%,transparent)}}.badge-red:where(.dark,.dark *){color:var(--color-red-400);--tw-inset-ring-color:#ff656833}@supports (color:color-mix(in lab,red,red)){.badge-red:where(.dark,.dark *){--tw-inset-ring-color:color-mix(in oklab,var(--color-red-400)20%,transparent)}}.badge-yellow{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);--tw-inset-ring-shadow:inset 0 0 0 1px var(--tw-inset-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-yellow-50);color:var(--color-yellow-800);--tw-inset-ring-color:#cd890033;align-items:center;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.badge-yellow{--tw-inset-ring-color:color-mix(in oklab,var(--color-yellow-600)20%,transparent)}}.badge-yellow:where(.dark,.dark *){background-color:#fac8001a}@supports (color:color-mix(in lab,red,red)){.badge-yellow:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-yellow-400)10%,transparent)}}.badge-yellow:where(.dark,.dark *){color:var(--color-yellow-400);--tw-inset-ring-color:#edb20033}@supports (color:color-mix(in lab,red,red)){.badge-yellow:where(.dark,.dark *){--tw-inset-ring-color:color-mix(in oklab,var(--color-yellow-500)20%,transparent)}}.badge-green{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);--tw-inset-ring-shadow:inset 0 0 0 1px var(--tw-inset-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-green-50);color:var(--color-green-700);--tw-inset-ring-color:#00a54433;align-items:center;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.badge-green{--tw-inset-ring-color:color-mix(in oklab,var(--color-green-600)20%,transparent)}}.badge-green:where(.dark,.dark *){background-color:#05df721a}@supports (color:color-mix(in lab,red,red)){.badge-green:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-green-400)10%,transparent)}}.badge-green:where(.dark,.dark *){color:var(--color-green-400);--tw-inset-ring-color:#00c75833}@supports (color:color-mix(in lab,red,red)){.badge-green:where(.dark,.dark *){--tw-inset-ring-color:color-mix(in oklab,var(--color-green-500)20%,transparent)}}.badge-blue{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);--tw-inset-ring-shadow:inset 0 0 0 1px var(--tw-inset-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-blue-50);color:var(--color-blue-700);--tw-inset-ring-color:#1447e61a;align-items:center;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.badge-blue{--tw-inset-ring-color:color-mix(in oklab,var(--color-blue-700)10%,transparent)}}.badge-blue:where(.dark,.dark *){background-color:#54a2ff1a}@supports (color:color-mix(in lab,red,red)){.badge-blue:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-blue-400)10%,transparent)}}.badge-blue:where(.dark,.dark *){color:var(--color-blue-400);--tw-inset-ring-color:#54a2ff4d}@supports (color:color-mix(in lab,red,red)){.badge-blue:where(.dark,.dark *){--tw-inset-ring-color:color-mix(in oklab,var(--color-blue-400)30%,transparent)}}.badge-indigo{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);--tw-inset-ring-shadow:inset 0 0 0 1px var(--tw-inset-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-indigo-50);color:var(--color-indigo-700);--tw-inset-ring-color:#432dd71a;align-items:center;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.badge-indigo{--tw-inset-ring-color:color-mix(in oklab,var(--color-indigo-700)10%,transparent)}}.badge-indigo:where(.dark,.dark *){background-color:#7d87ff1a}@supports (color:color-mix(in lab,red,red)){.badge-indigo:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-indigo-400)10%,transparent)}}.badge-indigo:where(.dark,.dark *){color:var(--color-indigo-400);--tw-inset-ring-color:#7d87ff4d}@supports (color:color-mix(in lab,red,red)){.badge-indigo:where(.dark,.dark *){--tw-inset-ring-color:color-mix(in oklab,var(--color-indigo-400)30%,transparent)}}.badge-purple{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);--tw-inset-ring-shadow:inset 0 0 0 1px var(--tw-inset-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-purple-50);color:var(--color-purple-700);--tw-inset-ring-color:#8200da1a;align-items:center;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.badge-purple{--tw-inset-ring-color:color-mix(in oklab,var(--color-purple-700)10%,transparent)}}.badge-purple:where(.dark,.dark *){background-color:#c07eff1a}@supports (color:color-mix(in lab,red,red)){.badge-purple:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-purple-400)10%,transparent)}}.badge-purple:where(.dark,.dark *){color:var(--color-purple-400);--tw-inset-ring-color:#c07eff4d}@supports (color:color-mix(in lab,red,red)){.badge-purple:where(.dark,.dark *){--tw-inset-ring-color:color-mix(in oklab,var(--color-purple-400)30%,transparent)}}.badge-pink{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);--tw-inset-ring-shadow:inset 0 0 0 1px var(--tw-inset-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-pink-50);color:var(--color-pink-700);--tw-inset-ring-color:#c4005c1a;align-items:center;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.badge-pink{--tw-inset-ring-color:color-mix(in oklab,var(--color-pink-700)10%,transparent)}}.badge-pink:where(.dark,.dark *){background-color:#fb64b61a}@supports (color:color-mix(in lab,red,red)){.badge-pink:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-pink-400)10%,transparent)}}.badge-pink:where(.dark,.dark *){color:var(--color-pink-400);--tw-inset-ring-color:#fb64b64d}@supports (color:color-mix(in lab,red,red)){.badge-pink:where(.dark,.dark *){--tw-inset-ring-color:color-mix(in oklab,var(--color-pink-400)30%,transparent)}}.badge-gray{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);--tw-inset-ring-shadow:inset 0 0 0 1px var(--tw-inset-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-gray-50);color:var(--color-gray-600);--tw-inset-ring-color:#6a72821a;align-items:center;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.badge-gray{--tw-inset-ring-color:color-mix(in oklab,var(--color-gray-500)10%,transparent)}}.badge-gray:where(.dark,.dark *){background-color:#99a1af1a}@supports (color:color-mix(in lab,red,red)){.badge-gray:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-gray-400)10%,transparent)}}.badge-gray:where(.dark,.dark *){color:var(--color-gray-400);--tw-inset-ring-color:#99a1af33}@supports (color:color-mix(in lab,red,red)){.badge-gray:where(.dark,.dark *){--tw-inset-ring-color:color-mix(in oklab,var(--color-gray-400)20%,transparent)}}button,a{cursor:pointer}:is(button,a).primary{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*1.5);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-primary-600);color:var(--color-white);justify-content:center;align-items:center;display:flex}@media (hover:hover){:is(button,a).primary:hover{background-color:var(--color-primary-700)}}:is(button,a).primary:where(.dark,.dark *){background-color:var(--color-primary-500)}@media (hover:hover){:is(button,a).primary:where(.dark,.dark *):hover{background-color:var(--color-primary-600)}}:is(button,a).red{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*1.5);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-red-600);color:var(--color-white);justify-content:center;align-items:center;display:flex}@media (hover:hover){:is(button,a).red:hover{background-color:var(--color-red-700)}}:is(button,a).red:where(.dark,.dark *){background-color:var(--color-red-500)}@media (hover:hover){:is(button,a).red:where(.dark,.dark *):hover{background-color:var(--color-red-600)}}:is(button,a).black{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*1.5);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-black);color:var(--color-white);justify-content:center;align-items:center;display:flex}@media (hover:hover){:is(button,a).black:hover{background-color:var(--color-gray-800)}}:is(button,a).black:where(.dark,.dark *){background-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){:is(button,a).black:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-white)10%,transparent)}}:is(button,a).black:where(.dark,.dark *){color:var(--color-white)}@media (hover:hover){:is(button,a).black:where(.dark,.dark *):hover{background-color:#fff3}@supports (color:color-mix(in lab,red,red)){:is(button,a).black:where(.dark,.dark *):hover{background-color:color-mix(in oklab,var(--color-white)20%,transparent)}}}:is(button,a).green{border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*1.5);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);background-color:var(--color-green-600);color:var(--color-white);justify-content:center;align-items:center;display:flex}@media (hover:hover){:is(button,a).green:hover{background-color:var(--color-green-700)}}:is(button,a).green:where(.dark,.dark *){background-color:var(--color-green-500)}@media (hover:hover){:is(button,a).green:where(.dark,.dark *):hover{background-color:var(--color-green-600)}}label{font-size:var(--text-sm);line-height:calc(var(--spacing)*6);--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:var(--color-gray-900);display:block}@media (min-width:40rem){label{padding-top:calc(var(--spacing)*1.5)}}label:where(.dark,.dark *){color:var(--color-white)}input,textarea{border-radius:var(--radius-md);background-color:var(--color-white);width:100%;padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*1.5);font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height));color:var(--color-gray-900);outline-style:var(--tw-outline-style);outline-offset:-1px;outline-width:1px;outline-color:var(--color-gray-300);display:block}:is(input,textarea)::placeholder{color:var(--color-gray-400)}:is(input,textarea):focus{outline-style:var(--tw-outline-style);outline-offset:-2px;outline-width:2px;outline-color:var(--color-primary-600)}@media (min-width:40rem){input,textarea{font-size:var(--text-sm);line-height:calc(var(--spacing)*6)}}:is(input,textarea):where(.dark,.dark *){background-color:#ffffff0d}@supports (color:color-mix(in lab,red,red)){:is(input,textarea):where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-white)5%,transparent)}}:is(input,textarea):where(.dark,.dark *){color:var(--color-white);outline-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){:is(input,textarea):where(.dark,.dark *){outline-color:color-mix(in oklab,var(--color-white)10%,transparent)}}:is(input,textarea):where(.dark,.dark *)::placeholder{color:var(--color-gray-500)}:is(input,textarea):where(.dark,.dark *):focus{outline-color:var(--color-primary-500)}select{-webkit-appearance:none;-moz-appearance:none;appearance:none;border-radius:var(--radius-md);background-color:var(--color-white);width:100%;padding-block:calc(var(--spacing)*1.5);padding-right:calc(var(--spacing)*8);padding-left:calc(var(--spacing)*3);font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height));color:var(--color-gray-900);outline-style:var(--tw-outline-style);outline-offset:-1px;outline-width:1px;outline-color:var(--color-gray-300);grid-row-start:1;grid-column-start:1}select:focus{outline-style:var(--tw-outline-style);outline-offset:-2px;outline-width:2px;outline-color:var(--color-primary-600)}@media (min-width:40rem){select{font-size:var(--text-sm);line-height:calc(var(--spacing)*6)}}select:where(.dark,.dark *){background-color:#ffffff0d}@supports (color:color-mix(in lab,red,red)){select:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-white)5%,transparent)}}select:where(.dark,.dark *){color:var(--color-white);outline-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){select:where(.dark,.dark *){outline-color:color-mix(in oklab,var(--color-white)10%,transparent)}}:is(select:where(.dark,.dark *)>*){background-color:var(--color-gray-800)}select:where(.dark,.dark *):focus{outline-color:var(--color-primary-500)}.heading{margin-bottom:calc(var(--spacing)*4);justify-content:space-between;align-items:center;gap:calc(var(--spacing)*4);display:flex}.heading div{justify-content:space-between;width:100%;display:flex}.heading-title{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height));--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold);color:var(--color-black)}.heading-title:where(.dark,.dark *){color:var(--color-white)}.cell-simple{height:calc(var(--spacing)*24);border-radius:var(--radius-sm);background-color:var(--color-gray-100);padding:calc(var(--spacing)*4);font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height));--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold);color:var(--color-gray-400);justify-content:center;align-items:center;display:flex}.cell-simple:where(.dark,.dark *){color:var(--color-gray-300);background-color:#212121}.cell{border-radius:var(--radius-sm);background-color:var(--color-gray-100);padding:calc(var(--spacing)*4)}.cell:where(.dark,.dark *){background-color:#212121}.cell-content{align-items:center;width:100%;display:flex}.cell-title{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height));--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold);color:var(--color-gray-800)}.cell-title:where(.dark,.dark *){color:var(--color-gray-300)}.cell-text{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height));--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold);color:var(--color-gray-800)}.cell-text:where(.dark,.dark *){color:var(--color-gray-300)}.cell-note{margin-top:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:var(--color-gray-500)}.cell-note:where(.dark,.dark *){color:var(--color-gray-400)}.cell-note-mobile{margin-top:calc(var(--spacing)*4);row-gap:calc(var(--spacing)*2);flex-direction:column;display:flex}@media (min-width:40rem){.cell-note-mobile{display:none}}.cell-note-mobile section{justify-content:space-between;width:100%;display:flex}:where(.cell-note-mobile section>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}.icon{margin-right:calc(var(--spacing)*2)}.form-label{margin-bottom:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:var(--color-gray-600)}.form-label:where(.dark,.dark *){color:var(--color-gray-400)}.form-input{border-radius:var(--radius-lg);border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-gray-300);background-color:var(--color-gray-50);width:100%;padding:calc(var(--spacing)*2.5);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:var(--color-gray-900);display:block}.form-input:where(.dark,.dark *){border-color:var(--color-gray-600);background-color:var(--color-gray-700);color:var(--color-white)}.form-buttons{border-top-style:var(--tw-border-style);border-top-width:1px;border-color:var(--color-gray-200);padding:calc(var(--spacing)*5);padding-left:calc(var(--spacing)*0);border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem;align-items:center;display:flex}.form-buttons:where(.dark,.dark *){border-color:var(--color-gray-600)}.form-submit{border-radius:var(--radius-lg);background-color:var(--color-blue-700);padding-inline:calc(var(--spacing)*4);padding-block:calc(var(--spacing)*2);text-align:center;font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:var(--color-white)}@media (hover:hover){.form-submit:hover{background-color:var(--color-blue-800)}}.form-submit:where(.dark,.dark *){background-color:var(--color-blue-600)}@media (hover:hover){.form-submit:where(.dark,.dark *):hover{background-color:var(--color-blue-700)}}.form-reset{border-radius:var(--radius-lg);border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-gray-200);background-color:var(--color-white);padding-inline:calc(var(--spacing)*4);padding-block:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:var(--color-gray-500);margin-inline-start:calc(var(--spacing)*3)}@media (hover:hover){.form-reset:hover{background-color:var(--color-gray-100);color:var(--color-gray-900)}}.form-reset:where(.dark,.dark *){border-color:var(--color-gray-500);background-color:var(--color-gray-700);color:var(--color-gray-300)}@media (hover:hover){.form-reset:where(.dark,.dark *):hover{background-color:var(--color-gray-600);color:var(--color-white)}}.file-input{width:100%;font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:var(--color-gray-500);display:block}.file-input::file-selector-button{margin-inline-end:calc(var(--spacing)*4)}.file-input::file-selector-button{border-radius:var(--radius-lg)}.file-input::file-selector-button{border-style:var(--tw-border-style);border-width:0}.file-input::file-selector-button{background-color:var(--color-blue-600)}.file-input::file-selector-button{padding-inline:calc(var(--spacing)*4)}.file-input::file-selector-button{padding-block:calc(var(--spacing)*2)}.file-input::file-selector-button{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.file-input::file-selector-button{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.file-input::file-selector-button{color:var(--color-white)}@media (hover:hover){.file-input:hover::file-selector-button{background-color:var(--color-blue-700)}}.file-input:where(.dark,.dark *){color:var(--color-neutral-500)}.file-input:where(.dark,.dark *)::file-selector-button{background-color:var(--color-blue-500)}@media (hover:hover){.file-input:where(.dark,.dark *):hover::file-selector-button{background-color:var(--color-blue-400)}}.feedback-wrapper{max-width:var(--container-xs);row-gap:calc(var(--spacing)*4);flex-direction:column;margin-inline:auto;display:flex}.feedback-title{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height));color:var(--color-black)}.feedback-title:where(.dark,.dark *){color:var(--color-white)}.feedback-value{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height));--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold);--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight);color:var(--color-black);order:-9999}@media (min-width:40rem){.feedback-value{font-size:var(--text-5xl);line-height:var(--tw-leading,var(--text-5xl--line-height))}}.feedback-value:where(.dark,.dark *){color:var(--color-white)}.vehicle-type{cursor:pointer;border-radius:var(--radius-lg);border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-gray-200);background-color:var(--color-white);width:100%;padding:calc(var(--spacing)*3);text-align:center;color:var(--color-gray-500);align-items:center;display:flex}.vehicle-type:is(:where(.peer):checked~*){border-color:var(--color-blue-600);color:var(--color-blue-600)}@media (hover:hover){.vehicle-type:hover{background-color:var(--color-gray-100);color:var(--color-gray-600)}}@media (min-width:48rem){.vehicle-type{padding:calc(var(--spacing)*5)}}.vehicle-type:where(.dark,.dark *){border-color:var(--color-gray-700);color:var(--color-gray-400);background-color:#212121}.vehicle-type:where(.dark,.dark *):is(:where(.peer):checked~*){border-color:var(--color-blue-600);color:var(--color-blue-500)}@media (hover:hover){.vehicle-type:where(.dark,.dark *):hover{background-color:var(--color-gray-700);color:var(--color-gray-300)}}.form-offset{margin-top:calc(var(--spacing)*10)}:where(.form-offset>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*10)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*10)*calc(1 - var(--tw-space-y-reverse)))}.form-offset{border-bottom-style:var(--tw-border-style);border-color:#1018281a;border-bottom-width:1px}@supports (color:color-mix(in lab,red,red)){.form-offset{border-color:color-mix(in oklab,var(--color-gray-900)10%,transparent)}}.form-offset{padding-bottom:calc(var(--spacing)*12)}@media (min-width:40rem){:where(.form-offset>:not(:last-child)){--tw-space-y-reverse:0;--tw-divide-y-reverse:0;border-bottom-style:var(--tw-border-style);border-top-style:var(--tw-border-style);border-top-width:calc(1px*var(--tw-divide-y-reverse));border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)));border-color:#1018281a;margin-block-start:calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))}@supports (color:color-mix(in lab,red,red)){:where(.form-offset>:not(:last-child)){border-color:color-mix(in oklab,var(--color-gray-900)10%,transparent)}}.form-offset{border-top-style:var(--tw-border-style);padding-bottom:calc(var(--spacing)*0);border-top-width:1px}}.form-offset:where(.dark,.dark *){border-color:#f9fafb80}@supports (color:color-mix(in lab,red,red)){.form-offset:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-gray-50)50%,transparent)}}@media (min-width:40rem){:where(.form-offset:where(.dark,.dark *)>:not(:last-child)){border-color:#f9fafb80}@supports (color:color-mix(in lab,red,red)){:where(.form-offset:where(.dark,.dark *)>:not(:last-child)){border-color:color-mix(in oklab,var(--color-gray-50)50%,transparent)}}.row-wrapper{align-items:flex-start;gap:calc(var(--spacing)*4);padding-block:calc(var(--spacing)*6);grid-template-columns:repeat(3,minmax(0,1fr));display:grid}}.row-wrapper>div{margin-top:calc(var(--spacing)*2)}@media (min-width:40rem){.row-wrapper>div{margin-top:calc(var(--spacing)*0);grid-column:span 2/span 2}}.row-wrapper label{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-leading:calc(var(--spacing)*6);line-height:calc(var(--spacing)*6);--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:var(--color-gray-900);display:block}.row-wrapper label:where(.dark,.dark *){color:var(--color-white)}.helper-text{margin-top:calc(var(--spacing)*3);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-leading:calc(var(--spacing)*6);line-height:calc(var(--spacing)*6);color:var(--color-gray-600)}.helper-text:where(.dark,.dark *){color:var(--color-gray-400)}.users-input{border-radius:var(--radius-md);border-style:var(--tw-border-style);width:100%;padding-block:calc(var(--spacing)*1.5);padding-left:calc(var(--spacing)*1);color:var(--color-gray-900);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-gray-300);--tw-ring-inset:inset;border-width:1px;display:block}.users-input::placeholder{color:var(--color-gray-400)}.users-input:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.users-input:focus{outline-offset:2px;outline:2px solid #0000}}@media (min-width:40rem){.users-input{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-leading:calc(var(--spacing)*6);line-height:calc(var(--spacing)*6)}}.users-input:where(.dark,.dark *){border-color:var(--color-black);color:var(--color-white)}@property --tw-content{syntax:"*";inherits:false;initial-value:""}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-divide-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-leading{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@property --tw-text-shadow-color{syntax:"*";inherits:false}@property --tw-text-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}
