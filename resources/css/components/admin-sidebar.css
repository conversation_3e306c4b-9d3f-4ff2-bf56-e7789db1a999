@utility desktop-sidebar {
    @apply hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col;
}

@utility sidebar-menu {
    @apply relative flex grow flex-col gap-y-5 overflow-y-auto bg-primary-600 px-6 pb-4 dark:bg-primary-800 dark:after:pointer-events-none dark:after:absolute dark:after:inset-y-0 dark:after:right-0 dark:after:w-px dark:after:bg-white/10;
}

@utility sidebar-item {
    @apply flex items-center gap-x-3 rounded-md p-2 text-sm/6 font-semibold text-primary-200 dark:text-primary-100 hover:text-white hover:bg-primary-700 dark:hover:bg-primary-950/25;

    & i {
        @apply text-primary-200 dark:text-primary-100;
    }

    &.active {
        @apply bg-primary-700! dark:bg-primary-950/25! text-white!
    }
}

