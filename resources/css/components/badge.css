@utility badge-common {
    @apply inline-flex items-center rounded-md px-2 py-1 text-xs font-medium inset-ring;
}

.badge-red {
    @apply badge-common bg-red-50 text-red-700 inset-ring-red-600/10 dark:bg-red-400/10 dark:text-red-400 dark:inset-ring-red-400/20;
}

.badge-yellow {
    @apply badge-common bg-yellow-50 text-yellow-800 inset-ring-yellow-600/20 dark:bg-yellow-400/10 dark:text-yellow-400 dark:inset-ring-yellow-500/20;
}

.badge-green {
    @apply badge-common bg-green-50 text-green-700 inset-ring-green-600/20 dark:bg-green-400/10 dark:text-green-400 dark:inset-ring-green-500/20;
}

.badge-blue {
    @apply badge-common bg-blue-50 text-blue-700 inset-ring-blue-700/10 dark:bg-blue-400/10 dark:text-blue-400 dark:inset-ring-blue-400/30;
}

.badge-indigo {
    @apply badge-common bg-indigo-50 text-indigo-700 inset-ring-indigo-700/10 dark:bg-indigo-400/10 dark:text-indigo-400 dark:inset-ring-indigo-400/30;
}

.badge-purple {
    @apply badge-common bg-purple-50 text-purple-700 inset-ring-purple-700/10 dark:bg-purple-400/10 dark:text-purple-400 dark:inset-ring-purple-400/30;
}

.badge-pink {
    @apply badge-common bg-pink-50 text-pink-700 inset-ring-pink-700/10 dark:bg-pink-400/10 dark:text-pink-400 dark:inset-ring-pink-400/30;
}

.badge-gray {
    @apply badge-common bg-gray-50 text-gray-600 inset-ring-gray-500/10 dark:bg-gray-400/10 dark:text-gray-400 dark:inset-ring-gray-400/20;
}
