@utility alert-window {
    @apply flex w-full items-center p-4 text-gray-500 bg-white dark:text-gray-300 dark:bg-zinc-700 rounded-lg shadow-lg;
}

@utility alert-badge {
    @apply inline-flex items-center justify-center shrink-0 w-8 h-8 rounded-lg;
}

@utility alert-close {
    @apply ms-auto -mx-1.5 -my-1.5 text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 dark:hover:bg-white/10 dark:hover:text-white inline-flex items-center justify-center h-8 w-8;
}

@layer components {
    .alert-window .badge-blue {
        @apply alert-badge text-blue-700 bg-blue-50 dark:text-blue-400 dark:bg-blue-400/10;
    }

    .alert-window .badge-green {
        @apply alert-badge text-green-700 bg-green-50 dark:text-green-400 dark:bg-green-400/10;
    }

    .alert-window .badge-yellow {
        @apply alert-badge text-yellow-800 bg-yellow-50 dark:text-yellow-500 dark:bg-yellow-400/10;
    }

    .alert-window .badge-red {
        @apply alert-badge text-red-700 bg-red-50 dark:text-red-400 dark:bg-red-400/10;
    }
}
