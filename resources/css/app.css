@import 'tailwindcss';

@import "./components/admin-sidebar.css";
@import "./components/alert.css";
@import "./components/badge.css";
@import "./components/button.css";
@import "./components/form.css";

@custom-variant dark (&:where(.dark, .dark *));

@theme {
    --color-primary: var(--color-primary-600);

    --color-primary-50: oklch(0.97 0.014 254.604);
    --color-primary-100: oklch(0.932 0.032 255.585);
    --color-primary-200: oklch(0.882 0.059 254.128);
    --color-primary-300: oklch(0.809 0.105 251.813);
    --color-primary-400: oklch(0.707 0.165 254.624);
    --color-primary-500: oklch(0.623 0.214 259.815);
    --color-primary-600: oklch(0.546 0.245 262.881);
    --color-primary-700: oklch(0.488 0.243 264.376);
    --color-primary-800: oklch(0.424 0.199 265.638);
    --color-primary-900: oklch(0.379 0.146 265.522);
    --color-primary-950: oklch(0.282 0.091 267.935);
}

@layer base {
    * {
        font-family: "IBM Plex Sans", sans-serif;
    }
}

.heading {
  @apply flex justify-between items-center gap-4 mb-4;
  & div {
    @apply flex w-full justify-between;
  }
}

.heading-title {
  @apply text-3xl font-bold text-black dark:text-white;
}

.cell-simple {
  /* Cells */
  @apply flex items-center justify-center h-24 rounded-sm bg-gray-100 dark:bg-[#212121] p-4 text-2xl font-bold text-gray-400 dark:text-gray-300;
}

.cell {
  @apply rounded-sm bg-gray-100 dark:bg-[#212121] p-4;
}

.cell-content {
  @apply w-full flex items-center;
}

.cell-title {
  @apply text-2xl font-bold text-gray-800 dark:text-gray-300;
}

.cell-text {
  @apply text-2xl font-bold text-gray-800 dark:text-gray-300;
}

.cell-note {
  @apply text-sm text-gray-500 dark:text-gray-400 mt-2;
}

.cell-note-mobile {
  @apply mt-4 flex flex-col gap-y-2 sm:hidden;
  & section {
    @apply flex w-full space-x-2 justify-between;
  }
}

.icon {
  @apply mr-2;
}

.form-label {
  /* Forms */
  @apply mb-2 text-sm font-medium text-gray-600 dark:text-gray-400;
}

.form-input {
  @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white;
}

.form-buttons {
  @apply flex items-center p-5 pl-0 border-t border-gray-200 rounded-b dark:border-gray-600;
}

.form-submit {
  @apply text-white bg-blue-700 hover:bg-blue-800 font-medium rounded-lg text-sm px-4 py-2 text-center dark:bg-blue-600 dark:hover:bg-blue-700;
}

.form-reset {
  @apply ms-3 text-gray-500 bg-white hover:bg-gray-100 rounded-lg border border-gray-200 text-sm font-medium px-4 py-2 hover:text-gray-900 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600;
}

.file-input {
  @apply block w-full text-sm text-gray-500 file:me-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-600 file:text-white hover:file:bg-blue-700 file:disabled:opacity-50 file:disabled:pointer-events-none dark:text-neutral-500 dark:file:bg-blue-500 dark:hover:file:bg-blue-400
}

.feedback-wrapper {
  /* Feedbacks */
  @apply mx-auto flex max-w-xs flex-col gap-y-4;
}

.feedback-title {
  @apply text-base text-black dark:text-white;
}

.feedback-value {
  @apply order-first text-3xl font-bold tracking-tight text-black dark:text-white sm:text-5xl;
}

.vehicle-type {
  /* Vehicle park */
  @apply flex items-center text-center w-full p-3 md:p-5 text-gray-500 bg-white border border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 peer-checked:border-blue-600 dark:peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:bg-[#212121] dark:hover:bg-gray-700;
}

.form-offset {
  @apply mt-10 space-y-10 border-b border-gray-900/10 pb-12 sm:space-y-0 sm:divide-y sm:divide-gray-900/10 sm:border-t sm:pb-0 dark:border-gray-50/50 dark:sm:divide-gray-50/50;
}

.row-wrapper {
  @apply sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-6;
  & > div {
    @apply mt-2 sm:col-span-2 sm:mt-0;
  }
  & label {
    @apply block text-sm font-medium leading-6 text-gray-900 dark:text-white;
  }
}

.helper-text {
  @apply mt-3 text-sm leading-6 text-gray-600 dark:text-gray-400;
}

.users-input {
  @apply block w-full rounded-md border py-1.5 pl-1 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:outline-hidden sm:text-sm sm:leading-6 dark:text-white dark:border-black;
}
